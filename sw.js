// DERS PWA Service Worker
// Version: 1.0.0
// Silent operation - no console logs

const CACHE_NAME = 'ders-pwa-v1';
const urlsToCache = [
  '/ders/',
  '/ders/public/assets/css/bootstrap.min.css',
  '/ders/public/assets/js/bootstrap.bundle.min.js',
  '/ders/public/assets/fontawesome/fontawesome-free-6.4.0-web/css/all.min.css'
];

// Install event - cache resources silently
self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        return cache.addAll(urlsToCache);
      })
      .catch(function(error) {
        // Silent error handling
      })
  );
});

// Fetch event - serve from cache when possible, no logging
self.addEventListener('fetch', function(event) {
  // Skip caching for external resources and API calls
  if (event.request.url.includes('generativelanguage.googleapis.com') ||
      event.request.url.includes('cdnjs.cloudflare.com') ||
      event.request.url.includes('api.') ||
      event.request.method !== 'GET') {
    
    // Fetch from network without caching or logging
    event.respondWith(fetch(event.request));
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Return cached version or fetch from network silently
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
      .catch(function(error) {
        // Silent error handling
      })
  );
});

// Activate event - clean up old caches silently
self.addEventListener('activate', function(event) {
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Handle skip waiting message
self.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
